import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/sub_header_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

/// A page that displays a list of question parts with progress indicators
///
/// This page shows question parts in a clean, organized list format with:
/// - Progress bars for each item showing completion status
/// - Mandatory indicators for required items
/// - Consistent styling following app design patterns
/// - Optimized performance with separated widget components
@RoutePage()
class SubHeaderPage extends StatefulWidget {
  final String title;
  final List<entities.QuestionPart> questionParts;
  final entities.Question question;
  final num? taskId;
  final num? formId;

  const SubHeaderPage({
    super.key,
    required this.title,
    required this.questionParts,
    required this.question,
    this.taskId,
    this.formId,
  });

  @override
  State<SubHeaderPage> createState() => _SubHeaderPageState();
}

class _SubHeaderPageState extends State<SubHeaderPage> {
  /// Check if a question is mandatory based on the complex logic provided
  bool _isQuestionMandatory(entities.Question question) {
    // Check for non-multi and non-comment questions
    if (question.isMulti != true && question.isComment != true) {
      // Loop through measurements to check if any validation is required
      if (question.measurements != null) {
        for (final measurement in question.measurements!) {
          if (measurement.measurementValidations != null) {
            for (final validation in measurement.measurementValidations!) {
              if (validation.required == true) {
                return true;
              }
            }
          }
        }
      }
    } else if (question.isMulti != true) {
      // Check if isSupplementaryQuestion is true and isOneOption is true
      final isSupplementaryQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId.toString() != "0" &&
          question.multiMeasurementId.toString().isNotEmpty;

      if (isSupplementaryQuestion) {
        // Check if isOneOption is true (any questionpart_id contains "-")
        if (question.questionParts != null) {
          for (final questionPart in question.questionParts!) {
            if (questionPart.questionpartId != null &&
                questionPart.questionpartId.toString().contains("-")) {
              return true;
            }
          }
        }
      }
    }

    return false;
  }

  /// Handle navigation to QPMD page and refresh state when returning
  Future<void> _handleQuestionPartTap(
      entities.QuestionPart questionPart) async {
    await context.router.push(QPMDRoute(
      question: widget.question,
      questionPart: questionPart,
      taskId: widget.taskId,
      formId: widget.formId,
    ));

    // Refresh the page state when returning from QPMD page
    if (mounted) {
      setState(() {
        // This will trigger a rebuild and refresh progress calculations
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final isMandatory = _isQuestionMandatory(widget.question);

    return Scaffold(
      backgroundColor: AppColors.lightGrey1,
      appBar: CustomAppBar(
        title: widget.title,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(bottom: 90), // Add space for FAB
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Gap(8),
              widget.questionParts.isEmpty
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          'No items available for this section',
                          style: textTheme.bodyLarge,
                        ),
                      ),
                    )
                  : ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      itemCount: widget.questionParts.length,
                      itemBuilder: (context, index) {
                        final part = widget.questionParts[index];
                        return SubHeaderCard(
                          questionPart: part,
                          question: widget.question,
                          isMandatory: isMandatory,
                          taskId: widget.taskId,
                          formId: widget.formId,
                          onTap: () => _handleQuestionPartTap(part),
                        );
                      },
                      separatorBuilder: (_, __) => const Gap(8),
                    ),
              const Gap(8),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: SizedBox(
          width: double.infinity,
          child: AppButton(
            text: 'Add Signature',
            color: AppColors.primaryBlue,
            onPressed: () {
              // TODO: Implement signature capture functionality
              // context.router.push(SignatureCaptureRoute());
            },
          ),
        ),
      ),
    );
  }
}
