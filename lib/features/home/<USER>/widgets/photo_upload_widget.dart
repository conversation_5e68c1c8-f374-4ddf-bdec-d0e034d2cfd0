import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart'; // Assuming this is your custom theme extension
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'dart:io';

class PhotoUploadWidget extends StatelessWidget {
  /// A list of local image file paths to display as thumbnails.
  final List<String> selectedImages;

  /// A callback triggered when the header area (containing "Add photos") is tapped.
  /// Use this to initiate adding a new photo (e.g., by showing a modal sheet).
  final VoidCallback? onCameraPressed;

  /// A callback triggered when the row of displayed images is tapped.
  /// Use this to navigate to a gallery or detail view.
  final VoidCallback? onImagesTap;

  /// Error message to display below the photo upload widget
  final String? errorText;

  /// PhotoTag object containing photo configuration settings
  final PhotoTagsT? photoTag;

  const PhotoUploadWidget({
    super.key,
    this.selectedImages = const [],
    this.onCameraPressed,
    this.onImagesTap,
    this.errorText,
    this.photoTag,
  });

  /// Get appropriate image provider based on whether the path is a URL or local file
  ImageProvider _getImageProvider(String imagePath) {
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      // Network image
      logger('Loading network image: $imagePath');
      return NetworkImage(imagePath);
    } else {
      // Local file
      logger('Loading local file: $imagePath');
      return FileImage(File(imagePath));
    }
  }

  /// Get camera icon color based on is_mandatory property
  Color _getCameraIconColor() {
    if (photoTag?.isMandatory == true) {
      return Colors.orange; // Yellow color for mandatory
    } else {
      return Colors.green; // Green color for optional
    }
  }

  /// Determine if HI-RES text should be shown based on image_rec property
  bool _shouldShowHiResText() {
    return photoTag?.imageRec == true;
  }

  /// Get image quality for compression based on PhotoResPerc
  /// Returns null if compression should be disabled (when image_rec is true)
  int? _getImageQuality() {
    if (photoTag?.imageRec == true) {
      // Disable compression for high-resolution images
      return null;
    }

    // Use PhotoResPerc as compression percentage, default to 85 if not specified
    final photoResPerc = photoTag?.photoResPerc?.toInt();
    return photoResPerc ?? 85;
  }

  /// Check if only live images (camera) are allowed
  bool _isLiveImagesOnly() {
    return photoTag?.liveImagesOnly == true;
  }

  @override
  Widget build(BuildContext context) {
    final bool hasImages = selectedImages.isNotEmpty;
    // Assuming your theme extension is available on the BuildContext
    final textTheme = Theme.of(context).textTheme;

    // Determine camera icon color based on is_mandatory
    final Color cameraIconColor = _getCameraIconColor();

    // Determine if HI-RES text should be shown
    final bool showHiResText = _shouldShowHiResText();

    return Container(
      margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppColors.borderBlack,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 12, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: onCameraPressed,
              borderRadius: BorderRadius.circular(10),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Add photos',
                            style: textTheme.montserratTitleExtraSmall,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Clearly show each location',
                            style: textTheme.montserratTableSmall,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    // This Row is part of the tappable area
                    Row(
                      children: [
                        // Show HI-RES text only if image_rec is true
                        if (showHiResText) ...[
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade300,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 10,
                              vertical: 6,
                            ),
                            child: Text(
                              'HI-RES',
                              style: textTheme.montserratTitleSmall.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 10,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                        ],
                        Icon(
                          Icons.camera_alt_outlined,
                          color: cameraIconColor,
                          size: 24,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            if (hasImages) ...[
              const SizedBox(height: 16),
              Container(height: 1, color: Colors.grey.shade300),
              const SizedBox(height: 16),
              InkWell(
                onTap: onImagesTap,
                borderRadius: BorderRadius.circular(8),
                child: Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 60,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: selectedImages.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.grey.shade200,
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image(
                                    image: _getImageProvider(
                                        selectedImages[index]),
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 60,
                                        height: 60,
                                        color: Colors.grey.shade300,
                                        child: Icon(
                                          Icons.broken_image,
                                          color: Colors.grey.shade600,
                                          size: 24,
                                        ),
                                      );
                                    },
                                    loadingBuilder:
                                        (context, child, loadingProgress) {
                                      if (loadingProgress == null) return child;
                                      return Container(
                                        width: 60,
                                        height: 60,
                                        color: Colors.grey.shade200,
                                        child: Center(
                                          child: SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              value: loadingProgress
                                                          .expectedTotalBytes !=
                                                      null
                                                  ? loadingProgress
                                                          .cumulativeBytesLoaded /
                                                      loadingProgress
                                                          .expectedTotalBytes!
                                                  : null,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      Icons.chevron_right,
                      color: Colors.grey.shade600,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ],
            // Error text display
            if (errorText != null) ...[
              const SizedBox(height: 8),
              Text(
                errorText!,
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
